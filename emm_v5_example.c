/**********************************************************
*** Emm_V5.0闭环步进电机驱动库 - HAL库移植版本使用示例
*** 原作者：ZHANGDATOU  
*** HAL移植：Assistant
*** 支持平台：STM32F1xx HAL库
**********************************************************/

#include "emm_v5_hal.h"
#include "main.h"  // 包含HAL库相关头文件

// 全局变量
EMM_V5_HandleTypeDef hemm_v5;
extern UART_HandleTypeDef huart1;  // 假设使用UART1

// 电机实时位置全局变量
float Motor_Cur_Pos = 0.0f;

/**
 * @brief  EMM_V5驱动初始化
 * @param  无
 * @retval 无
 */
void EMM_V5_Example_Init(void)
{
    // 初始化EMM_V5驱动
    EMM_V5_Init(&hemm_v5, &huart1);
    
    // 上电延时2秒等待Emm_V5.0闭环驱动初始化完成
    HAL_Delay(2000);
}

/**
 * @brief  基本位置控制示例
 * @param  无
 * @retval 无
 */
void EMM_V5_Position_Control_Example(void)
{
    // 位置模式控制：CW方向，速度1000RPM，加速度0（直接启动），脉冲数3200
    // 16细分下，3200脉冲数对应电机转一圈，相对运动
    EMM_V5_Pos_Control(&hemm_v5, 1, 0, 1000, 0, 3200, 0, 0);
    
    // 等待电机响应
    if (EMM_V5_Wait_Response(&hemm_v5, 1000)) {
        // 检查响应是否正确
        if (EMM_V5_Check_Response(&hemm_v5, 1, 0xFD)) {
            // 响应正确，命令发送成功
        }
    }
    
    // 延时2秒，等待运动完成
    HAL_Delay(2000);
}

/**
 * @brief  读取电机实时位置示例
 * @param  无
 * @retval 无
 */
void EMM_V5_Read_Position_Example(void)
{
    // 读取电机实时位置
    EMM_V5_Read_Sys_Params(&hemm_v5, 1, S_CPOS);
    
    // 等待电机响应
    if (EMM_V5_Wait_Response(&hemm_v5, 1000)) {
        // 解析位置数据
        Motor_Cur_Pos = EMM_V5_Parse_Position(&hemm_v5, 1);
        
        // 这里可以使用Motor_Cur_Pos变量，它包含了当前电机位置角度
        // 例如：通过串口打印或者其他处理
    }
}

/**
 * @brief  速度控制示例
 * @param  无
 * @retval 无
 */
void EMM_V5_Velocity_Control_Example(void)
{
    // 速度模式控制：CW方向，速度500RPM，加速度10，不启用多机同步
    EMM_V5_Vel_Control(&hemm_v5, 1, 0, 500, 10, false);
    
    // 等待响应
    if (EMM_V5_Wait_Response(&hemm_v5, 1000)) {
        // 检查响应
        if (EMM_V5_Check_Response(&hemm_v5, 1, 0xF6)) {
            // 命令发送成功，电机开始以500RPM速度旋转
        }
    }
    
    // 运行3秒后停止
    HAL_Delay(3000);
    
    // 立即停止电机
    EMM_V5_Stop_Now(&hemm_v5, 1, false);
    
    // 等待停止响应
    EMM_V5_Wait_Response(&hemm_v5, 1000);
}

/**
 * @brief  使能控制示例
 * @param  无
 * @retval 无
 */
void EMM_V5_Enable_Control_Example(void)
{
    // 使能电机
    EMM_V5_En_Control(&hemm_v5, 1, true, false);
    HAL_Delay(100);
    
    // 进行一些运动控制...
    EMM_V5_Position_Control_Example();
    
    // 关闭电机使能
    EMM_V5_En_Control(&hemm_v5, 1, false, false);
    HAL_Delay(100);
}

/**
 * @brief  回零控制示例
 * @param  无
 * @retval 无
 */
void EMM_V5_Homing_Example(void)
{
    // 设置回零参数
    // 参数：地址1，存储设置，单圈就近回零，CW方向，回零速度100RPM，
    // 超时10秒，碰撞检测速度50RPM，碰撞电流100mA，碰撞时间100ms，不启用上电自动回零
    EMM_V5_Origin_Modify_Params(&hemm_v5, 1, true, 0, 0, 100, 10000, 50, 100, 100, false);
    
    // 等待参数设置响应
    if (EMM_V5_Wait_Response(&hemm_v5, 1000)) {
        HAL_Delay(100);
        
        // 触发回零
        EMM_V5_Origin_Trigger_Return(&hemm_v5, 1, 0, false);
        
        // 等待回零完成（这里应该根据实际情况调整超时时间）
        if (EMM_V5_Wait_Response(&hemm_v5, 15000)) {
            // 回零完成，可以进行后续操作
        }
    }
}

/**
 * @brief  多机同步运动示例
 * @param  无
 * @retval 无
 */
void EMM_V5_Sync_Motion_Example(void)
{
    // 假设有两个电机，地址分别为1和2
    
    // 电机1：位置控制，启用同步标志
    EMM_V5_Pos_Control(&hemm_v5, 1, 0, 1000, 0, 3200, 0, true);
    HAL_Delay(10);
    
    // 电机2：位置控制，启用同步标志（需要另一个EMM_V5_HandleTypeDef实例）
    // EMM_V5_Pos_Control(&hemm_v5_2, 2, 1, 1000, 0, 1600, 0, true);
    // HAL_Delay(10);
    
    // 发送同步运动命令，所有设置了同步标志的电机将同时开始运动
    EMM_V5_Synchronous_motion(&hemm_v5, 0xFF);  // 0xFF为广播地址
}

/**
 * @brief  完整的应用示例
 * @param  无
 * @retval 无
 */
void EMM_V5_Complete_Example(void)
{
    // 1. 初始化
    EMM_V5_Example_Init();
    
    // 2. 使能电机
    EMM_V5_En_Control(&hemm_v5, 1, true, false);
    HAL_Delay(100);
    
    // 3. 复位当前位置为零点
    EMM_V5_Reset_CurPos_To_Zero(&hemm_v5, 1);
    EMM_V5_Wait_Response(&hemm_v5, 1000);
    HAL_Delay(100);
    
    // 4. 位置控制运动
    EMM_V5_Position_Control_Example();
    
    // 5. 读取当前位置
    EMM_V5_Read_Position_Example();
    
    // 6. 速度控制运动
    EMM_V5_Velocity_Control_Example();
    
    // 7. 关闭电机使能
    EMM_V5_En_Control(&hemm_v5, 1, false, false);
    HAL_Delay(100);
}

/**
 * @brief  UART接收完成中断回调函数
 * @param  huart UART句柄
 * @retval 无
 * @note   需要在main.c中的HAL_UART_RxCpltCallback函数中调用此函数
 */
void HAL_UART_RxCpltCallback(UART_HandleTypeDef *huart)
{
    if (huart == hemm_v5.huart) {
        EMM_V5_UART_RxCpltCallback(&hemm_v5);
    }
}

/**
 * @brief  UART错误回调函数
 * @param  huart UART句柄
 * @retval 无
 * @note   需要在main.c中的HAL_UART_ErrorCallback函数中调用此函数
 */
void HAL_UART_ErrorCallback(UART_HandleTypeDef *huart)
{
    if (huart == hemm_v5.huart) {
        // 重新启动接收
        HAL_UART_Receive_IT(hemm_v5.huart, hemm_v5.rx_buffer, 1);
    }
}

/**
 * @brief  UART空闲中断处理
 * @param  huart UART句柄
 * @retval 无
 * @note   需要在stm32f1xx_it.c的USART1_IRQHandler中添加空闲中断处理
 */
void UART_IdleHandler(UART_HandleTypeDef *huart)
{
    if (huart == hemm_v5.huart) {
        if (__HAL_UART_GET_FLAG(huart, UART_FLAG_IDLE)) {
            __HAL_UART_CLEAR_IDLEFLAG(huart);
            EMM_V5_UART_IdleCallback(&hemm_v5);
        }
    }
}
