Component: ARM Compiler 5.06 update 5 (build 528) Tool: armlink [4d35e2]

==============================================================================

Section Cross References

    main.o(i.main) refers to board.o(i.board_init) for board_init
    main.o(i.main) refers to delay.o(i.delay_ms) for delay_ms
    main.o(i.main) refers to emm_v5.o(i.Emm_V5_Pos_Control) for Emm_V5_Pos_Control
    main.o(i.main) refers to emm_v5.o(i.Emm_V5_Read_Sys_Params) for Emm_V5_Read_Sys_Params
    main.o(i.main) refers to ffltui.o(.text) for __aeabi_ui2f
    main.o(i.main) refers to fmul.o(.text) for __aeabi_fmul
    main.o(i.main) refers to fdiv.o(.text) for __aeabi_fdiv
    main.o(i.main) refers to usart.o(.data) for rxFrameFlag
    main.o(i.main) refers to usart.o(.bss) for rxCmd
    main.o(i.main) refers to main.o(.data) for pos
    emm_v5.o(i.Emm_V5_En_Control) refers to usart.o(i.usart_SendCmd) for usart_SendCmd
    emm_v5.o(i.Emm_V5_Modify_Ctrl_Mode) refers to usart.o(i.usart_SendCmd) for usart_SendCmd
    emm_v5.o(i.Emm_V5_Origin_Interrupt) refers to usart.o(i.usart_SendCmd) for usart_SendCmd
    emm_v5.o(i.Emm_V5_Origin_Modify_Params) refers to memseta.o(.text) for __aeabi_memclr4
    emm_v5.o(i.Emm_V5_Origin_Modify_Params) refers to usart.o(i.usart_SendCmd) for usart_SendCmd
    emm_v5.o(i.Emm_V5_Origin_Set_O) refers to usart.o(i.usart_SendCmd) for usart_SendCmd
    emm_v5.o(i.Emm_V5_Origin_Trigger_Return) refers to usart.o(i.usart_SendCmd) for usart_SendCmd
    emm_v5.o(i.Emm_V5_Pos_Control) refers to usart.o(i.usart_SendCmd) for usart_SendCmd
    emm_v5.o(i.Emm_V5_Read_Sys_Params) refers to usart.o(i.usart_SendCmd) for usart_SendCmd
    emm_v5.o(i.Emm_V5_Reset_Clog_Pro) refers to usart.o(i.usart_SendCmd) for usart_SendCmd
    emm_v5.o(i.Emm_V5_Reset_CurPos_To_Zero) refers to usart.o(i.usart_SendCmd) for usart_SendCmd
    emm_v5.o(i.Emm_V5_Stop_Now) refers to usart.o(i.usart_SendCmd) for usart_SendCmd
    emm_v5.o(i.Emm_V5_Synchronous_motion) refers to usart.o(i.usart_SendCmd) for usart_SendCmd
    emm_v5.o(i.Emm_V5_Vel_Control) refers to usart.o(i.usart_SendCmd) for usart_SendCmd
    usart.o(i.USART1_IRQHandler) refers to stm32f10x_usart.o(i.USART_GetITStatus) for USART_GetITStatus
    usart.o(i.USART1_IRQHandler) refers to fifo.o(i.fifo_enQueue) for fifo_enQueue
    usart.o(i.USART1_IRQHandler) refers to stm32f10x_usart.o(i.USART_ClearITPendingBit) for USART_ClearITPendingBit
    usart.o(i.USART1_IRQHandler) refers to fifo.o(i.fifo_queueLength) for fifo_queueLength
    usart.o(i.USART1_IRQHandler) refers to fifo.o(i.fifo_deQueue) for fifo_deQueue
    usart.o(i.USART1_IRQHandler) refers to usart.o(.data) for rxCount
    usart.o(i.USART1_IRQHandler) refers to usart.o(.bss) for rxCmd
    usart.o(i.usart_SendCmd) refers to usart.o(i.usart_SendByte) for usart_SendByte
    board.o(i.board_init) refers to board.o(i.nvic_init) for nvic_init
    board.o(i.board_init) refers to board.o(i.clock_init) for clock_init
    board.o(i.board_init) refers to board.o(i.usart_init) for usart_init
    board.o(i.clock_init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    board.o(i.clock_init) refers to stm32f10x_gpio.o(i.GPIO_PinRemapConfig) for GPIO_PinRemapConfig
    board.o(i.nvic_init) refers to misc.o(i.NVIC_PriorityGroupConfig) for NVIC_PriorityGroupConfig
    board.o(i.nvic_init) refers to misc.o(i.NVIC_Init) for NVIC_Init
    board.o(i.usart_init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    board.o(i.usart_init) refers to stm32f10x_usart.o(i.USART_Init) for USART_Init
    board.o(i.usart_init) refers to stm32f10x_usart.o(i.USART_ClearITPendingBit) for USART_ClearITPendingBit
    board.o(i.usart_init) refers to stm32f10x_usart.o(i.USART_ITConfig) for USART_ITConfig
    board.o(i.usart_init) refers to stm32f10x_usart.o(i.USART_Cmd) for USART_Cmd
    startup_stm32f10x_hd.o(RESET) refers to startup_stm32f10x_hd.o(STACK) for __initial_sp
    startup_stm32f10x_hd.o(RESET) refers to startup_stm32f10x_hd.o(.text) for Reset_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(i.NMI_Handler) for NMI_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(i.HardFault_Handler) for HardFault_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(i.MemManage_Handler) for MemManage_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(i.BusFault_Handler) for BusFault_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(i.UsageFault_Handler) for UsageFault_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(i.SVC_Handler) for SVC_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(i.DebugMon_Handler) for DebugMon_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(i.PendSV_Handler) for PendSV_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(i.SysTick_Handler) for SysTick_Handler
    startup_stm32f10x_hd.o(RESET) refers to usart.o(i.USART1_IRQHandler) for USART1_IRQHandler
    startup_stm32f10x_hd.o(.text) refers to system_stm32f10x.o(i.SystemInit) for SystemInit
    startup_stm32f10x_hd.o(.text) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    system_stm32f10x.o(i.SetSysClock) refers to system_stm32f10x.o(i.SetSysClockTo72) for SetSysClockTo72
    system_stm32f10x.o(i.SystemCoreClockUpdate) refers to system_stm32f10x.o(.data) for SystemCoreClock
    system_stm32f10x.o(i.SystemInit) refers to system_stm32f10x.o(i.SetSysClock) for SetSysClock
    fifo.o(i.fifo_deQueue) refers to fifo.o(.bss) for rxFIFO
    fifo.o(i.fifo_enQueue) refers to fifo.o(.bss) for rxFIFO
    fifo.o(i.fifo_isEmpty) refers to fifo.o(.bss) for rxFIFO
    fifo.o(i.fifo_queueLength) refers to fifo.o(.bss) for rxFIFO
    fifo.o(i.initQueue) refers to fifo.o(.bss) for rxFIFO
    delay.o(i.delay_ms) refers to system_stm32f10x.o(.data) for SystemCoreClock
    stm32f10x_gpio.o(i.GPIO_AFIODeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_gpio.o(i.GPIO_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_rcc.o(i.RCC_GetClocksFreq) refers to stm32f10x_rcc.o(.data) for APBAHBPrescTable
    stm32f10x_rcc.o(i.RCC_WaitForHSEStartUp) refers to stm32f10x_rcc.o(i.RCC_GetFlagStatus) for RCC_GetFlagStatus
    stm32f10x_usart.o(i.USART_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_usart.o(i.USART_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_usart.o(i.USART_Init) refers to stm32f10x_rcc.o(i.RCC_GetClocksFreq) for RCC_GetClocksFreq
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry10a.o(.ARM.Collect$$$$0000000D) for __rt_final_cpp
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry11a.o(.ARM.Collect$$$$0000000F) for __rt_final_exit
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry7b.o(.ARM.Collect$$$$00000008) for _main_clock
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry8b.o(.ARM.Collect$$$$0000000A) for _main_cpp_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry9a.o(.ARM.Collect$$$$0000000B) for _main_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry5.o(.ARM.Collect$$$$00000004) for _main_scatterload
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry2.o(.ARM.Collect$$$$00000001) for _main_stk
    fmul.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fdiv.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fdiv.o(.text) refers to fepilogue.o(.text) for _float_round
    ffltui.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    ffltui.o(.text) refers to fepilogue.o(.text) for _float_epilogue
    entry2.o(.ARM.Collect$$$$00000001) refers to entry2.o(.ARM.Collect$$$$00002712) for __lit__00000000
    entry2.o(.ARM.Collect$$$$00002712) refers to startup_stm32f10x_hd.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to startup_stm32f10x_hd.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    entry5.o(.ARM.Collect$$$$00000004) refers to init.o(.text) for __scatterload
    entry9a.o(.ARM.Collect$$$$0000000B) refers to main.o(i.main) for main
    entry9b.o(.ARM.Collect$$$$0000000C) refers to main.o(i.main) for main
    init.o(.text) refers to entry5.o(.ARM.Collect$$$$00000004) for __main_after_scatterload


==============================================================================

Removing Unused input sections from the image.

    Removing emm_v5.o(i.Emm_V5_En_Control), (58 bytes).
    Removing emm_v5.o(i.Emm_V5_Modify_Ctrl_Mode), (58 bytes).
    Removing emm_v5.o(i.Emm_V5_Origin_Interrupt), (46 bytes).
    Removing emm_v5.o(i.Emm_V5_Origin_Modify_Params), (170 bytes).
    Removing emm_v5.o(i.Emm_V5_Origin_Set_O), (52 bytes).
    Removing emm_v5.o(i.Emm_V5_Origin_Trigger_Return), (52 bytes).
    Removing emm_v5.o(i.Emm_V5_Reset_Clog_Pro), (46 bytes).
    Removing emm_v5.o(i.Emm_V5_Reset_CurPos_To_Zero), (46 bytes).
    Removing emm_v5.o(i.Emm_V5_Stop_Now), (52 bytes).
    Removing emm_v5.o(i.Emm_V5_Synchronous_motion), (46 bytes).
    Removing emm_v5.o(i.Emm_V5_Vel_Control), (78 bytes).
    Removing core_cm3.o(.emb_text), (32 bytes).
    Removing startup_stm32f10x_hd.o(HEAP), (512 bytes).
    Removing system_stm32f10x.o(i.SystemCoreClockUpdate), (164 bytes).
    Removing fifo.o(i.fifo_isEmpty), (28 bytes).
    Removing fifo.o(i.initQueue), (20 bytes).
    Removing delay.o(i.delay_cnt), (10 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_AFIODeInit), (20 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_DeInit), (200 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ETH_MediaInterfaceConfig), (12 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_EXTILineConfig), (64 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_EventOutputCmd), (12 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_EventOutputConfig), (32 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_PinLockConfig), (18 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ReadInputData), (8 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ReadInputDataBit), (18 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ReadOutputData), (8 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ReadOutputDataBit), (18 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ResetBits), (4 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_SetBits), (4 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_StructInit), (16 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_Write), (4 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_WriteBit), (10 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ADCCLKConfig), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_AHBPeriphClockCmd), (32 bytes).
    Removing stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd), (32 bytes).
    Removing stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd), (32 bytes).
    Removing stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd), (32 bytes).
    Removing stm32f10x_rcc.o(i.RCC_AdjustHSICalibrationValue), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_BackupResetCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ClearFlag), (20 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ClearITPendingBit), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ClockSecuritySystemCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_DeInit), (76 bytes).
    Removing stm32f10x_rcc.o(i.RCC_GetFlagStatus), (60 bytes).
    Removing stm32f10x_rcc.o(i.RCC_GetITStatus), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_GetSYSCLKSource), (16 bytes).
    Removing stm32f10x_rcc.o(i.RCC_HCLKConfig), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_HSEConfig), (76 bytes).
    Removing stm32f10x_rcc.o(i.RCC_HSICmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ITConfig), (32 bytes).
    Removing stm32f10x_rcc.o(i.RCC_LSEConfig), (52 bytes).
    Removing stm32f10x_rcc.o(i.RCC_LSICmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_MCOConfig), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_PCLK1Config), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_PCLK2Config), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_PLLCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_PLLConfig), (28 bytes).
    Removing stm32f10x_rcc.o(i.RCC_RTCCLKCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_RTCCLKConfig), (16 bytes).
    Removing stm32f10x_rcc.o(i.RCC_SYSCLKConfig), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_USBCLKConfig), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_WaitForHSEStartUp), (56 bytes).
    Removing stm32f10x_usart.o(i.USART_ClearFlag), (18 bytes).
    Removing stm32f10x_usart.o(i.USART_ClockInit), (34 bytes).
    Removing stm32f10x_usart.o(i.USART_ClockStructInit), (12 bytes).
    Removing stm32f10x_usart.o(i.USART_DMACmd), (18 bytes).
    Removing stm32f10x_usart.o(i.USART_DeInit), (156 bytes).
    Removing stm32f10x_usart.o(i.USART_GetFlagStatus), (26 bytes).
    Removing stm32f10x_usart.o(i.USART_HalfDuplexCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_IrDACmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_IrDAConfig), (18 bytes).
    Removing stm32f10x_usart.o(i.USART_LINBreakDetectLengthConfig), (18 bytes).
    Removing stm32f10x_usart.o(i.USART_LINCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_OneBitMethodCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_OverSampling8Cmd), (22 bytes).
    Removing stm32f10x_usart.o(i.USART_ReceiveData), (10 bytes).
    Removing stm32f10x_usart.o(i.USART_ReceiverWakeUpCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_SendBreak), (10 bytes).
    Removing stm32f10x_usart.o(i.USART_SendData), (8 bytes).
    Removing stm32f10x_usart.o(i.USART_SetAddress), (18 bytes).
    Removing stm32f10x_usart.o(i.USART_SetGuardTime), (16 bytes).
    Removing stm32f10x_usart.o(i.USART_SetPrescaler), (16 bytes).
    Removing stm32f10x_usart.o(i.USART_SmartCardCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_SmartCardNACKCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_StructInit), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_WakeUpConfig), (18 bytes).
    Removing misc.o(i.NVIC_SetVectorTable), (20 bytes).
    Removing misc.o(i.NVIC_SystemLPConfig), (32 bytes).
    Removing misc.o(i.SysTick_CLKSourceConfig), (40 bytes).

90 unused section(s) (total 3456 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry5.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry2.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11a.o ABSOLUTE
    ../clib/microlib/string/memset.c         0x00000000   Number         0  memseta.o ABSOLUTE
    ../clib/microlib/stubs.s                 0x00000000   Number         0  iusefp.o ABSOLUTE
    ../fplib/microlib/fpdiv.c                0x00000000   Number         0  fdiv.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  fepilogue.o ABSOLUTE
    ../fplib/microlib/fpflt.c                0x00000000   Number         0  ffltui.o ABSOLUTE
    ../fplib/microlib/fpmul.c                0x00000000   Number         0  fmul.o ABSOLUTE
    ..\APP\main.c                            0x00000000   Number         0  main.o ABSOLUTE
    ..\APP\stm32f10x_it.c                    0x00000000   Number         0  stm32f10x_it.o ABSOLUTE
    ..\BSP\Emm_V5.c                          0x00000000   Number         0  emm_v5.o ABSOLUTE
    ..\BSP\board.c                           0x00000000   Number         0  board.o ABSOLUTE
    ..\BSP\usart.c                           0x00000000   Number         0  usart.o ABSOLUTE
    ..\CMSIS\core_cm3.c                      0x00000000   Number         0  core_cm3.o ABSOLUTE
    ..\CMSIS\startup_stm32f10x_hd.s          0x00000000   Number         0  startup_stm32f10x_hd.o ABSOLUTE
    ..\CMSIS\system_stm32f10x.c              0x00000000   Number         0  system_stm32f10x.o ABSOLUTE
    ..\DRIVERS\delay.c                       0x00000000   Number         0  delay.o ABSOLUTE
    ..\DRIVERS\fifo.c                        0x00000000   Number         0  fifo.o ABSOLUTE
    ..\LIB\STM32F10x_StdPeriph_Lib_V3.5.0\src\misc.c 0x00000000   Number         0  misc.o ABSOLUTE
    ..\LIB\STM32F10x_StdPeriph_Lib_V3.5.0\src\stm32f10x_gpio.c 0x00000000   Number         0  stm32f10x_gpio.o ABSOLUTE
    ..\LIB\STM32F10x_StdPeriph_Lib_V3.5.0\src\stm32f10x_rcc.c 0x00000000   Number         0  stm32f10x_rcc.o ABSOLUTE
    ..\LIB\STM32F10x_StdPeriph_Lib_V3.5.0\src\stm32f10x_usart.c 0x00000000   Number         0  stm32f10x_usart.o ABSOLUTE
    ..\\CMSIS\\core_cm3.c                    0x00000000   Number         0  core_cm3.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    handlers.s                               0x00000000   Number         0  handlers.o ABSOLUTE
    init.s                                   0x00000000   Number         0  init.o ABSOLUTE
    RESET                                    0x08000000   Section      304  startup_stm32f10x_hd.o(RESET)
    .ARM.Collect$$$$00000000                 0x08000130   Section        0  entry.o(.ARM.Collect$$$$00000000)
    .ARM.Collect$$$$00000001                 0x08000130   Section        4  entry2.o(.ARM.Collect$$$$00000001)
    .ARM.Collect$$$$00000004                 0x08000134   Section        4  entry5.o(.ARM.Collect$$$$00000004)
    .ARM.Collect$$$$00000008                 0x08000138   Section        0  entry7b.o(.ARM.Collect$$$$00000008)
    .ARM.Collect$$$$0000000A                 0x08000138   Section        0  entry8b.o(.ARM.Collect$$$$0000000A)
    .ARM.Collect$$$$0000000B                 0x08000138   Section        8  entry9a.o(.ARM.Collect$$$$0000000B)
    .ARM.Collect$$$$0000000D                 0x08000140   Section        0  entry10a.o(.ARM.Collect$$$$0000000D)
    .ARM.Collect$$$$0000000F                 0x08000140   Section        0  entry11a.o(.ARM.Collect$$$$0000000F)
    .ARM.Collect$$$$00002712                 0x08000140   Section        4  entry2.o(.ARM.Collect$$$$00002712)
    __lit__00000000                          0x08000140   Data           4  entry2.o(.ARM.Collect$$$$00002712)
    .text                                    0x08000144   Section       36  startup_stm32f10x_hd.o(.text)
    .text                                    0x08000168   Section        0  fmul.o(.text)
    .text                                    0x080001cc   Section        0  fdiv.o(.text)
    .text                                    0x08000248   Section        0  ffltui.o(.text)
    .text                                    0x08000252   Section        0  fepilogue.o(.text)
    .text                                    0x08000252   Section        0  iusefp.o(.text)
    .text                                    0x080002c0   Section       36  init.o(.text)
    i.BusFault_Handler                       0x080002e4   Section        0  stm32f10x_it.o(i.BusFault_Handler)
    i.DebugMon_Handler                       0x080002e8   Section        0  stm32f10x_it.o(i.DebugMon_Handler)
    i.Emm_V5_Pos_Control                     0x080002ea   Section        0  emm_v5.o(i.Emm_V5_Pos_Control)
    i.Emm_V5_Read_Sys_Params                 0x08000356   Section        0  emm_v5.o(i.Emm_V5_Read_Sys_Params)
    i.GPIO_Init                              0x0800045c   Section        0  stm32f10x_gpio.o(i.GPIO_Init)
    i.GPIO_PinRemapConfig                    0x08000574   Section        0  stm32f10x_gpio.o(i.GPIO_PinRemapConfig)
    i.HardFault_Handler                      0x08000604   Section        0  stm32f10x_it.o(i.HardFault_Handler)
    i.MemManage_Handler                      0x08000608   Section        0  stm32f10x_it.o(i.MemManage_Handler)
    i.NMI_Handler                            0x0800060c   Section        0  stm32f10x_it.o(i.NMI_Handler)
    i.NVIC_Init                              0x08000610   Section        0  misc.o(i.NVIC_Init)
    i.NVIC_PriorityGroupConfig               0x08000680   Section        0  misc.o(i.NVIC_PriorityGroupConfig)
    i.PendSV_Handler                         0x08000694   Section        0  stm32f10x_it.o(i.PendSV_Handler)
    i.RCC_APB2PeriphClockCmd                 0x08000698   Section        0  stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd)
    i.RCC_GetClocksFreq                      0x080006b8   Section        0  stm32f10x_rcc.o(i.RCC_GetClocksFreq)
    i.SVC_Handler                            0x0800078c   Section        0  stm32f10x_it.o(i.SVC_Handler)
    i.SetSysClock                            0x0800078e   Section        0  system_stm32f10x.o(i.SetSysClock)
    SetSysClock                              0x0800078f   Thumb Code     8  system_stm32f10x.o(i.SetSysClock)
    i.SetSysClockTo72                        0x08000798   Section        0  system_stm32f10x.o(i.SetSysClockTo72)
    SetSysClockTo72                          0x08000799   Thumb Code   214  system_stm32f10x.o(i.SetSysClockTo72)
    i.SysTick_Handler                        0x08000878   Section        0  stm32f10x_it.o(i.SysTick_Handler)
    i.SystemInit                             0x0800087c   Section        0  system_stm32f10x.o(i.SystemInit)
    i.USART1_IRQHandler                      0x080008dc   Section        0  usart.o(i.USART1_IRQHandler)
    i.USART_ClearITPendingBit                0x08000964   Section        0  stm32f10x_usart.o(i.USART_ClearITPendingBit)
    i.USART_Cmd                              0x08000982   Section        0  stm32f10x_usart.o(i.USART_Cmd)
    i.USART_GetITStatus                      0x0800099a   Section        0  stm32f10x_usart.o(i.USART_GetITStatus)
    i.USART_ITConfig                         0x080009ee   Section        0  stm32f10x_usart.o(i.USART_ITConfig)
    i.USART_Init                             0x08000a38   Section        0  stm32f10x_usart.o(i.USART_Init)
    i.UsageFault_Handler                     0x08000b10   Section        0  stm32f10x_it.o(i.UsageFault_Handler)
    i.__scatterload_copy                     0x08000b14   Section       14  handlers.o(i.__scatterload_copy)
    i.__scatterload_null                     0x08000b22   Section        2  handlers.o(i.__scatterload_null)
    i.__scatterload_zeroinit                 0x08000b24   Section       14  handlers.o(i.__scatterload_zeroinit)
    i.board_init                             0x08000b32   Section        0  board.o(i.board_init)
    i.clock_init                             0x08000b44   Section        0  board.o(i.clock_init)
    i.delay_ms                               0x08000b64   Section        0  delay.o(i.delay_ms)
    i.fifo_deQueue                           0x08000bc4   Section        0  fifo.o(i.fifo_deQueue)
    i.fifo_enQueue                           0x08000bf4   Section        0  fifo.o(i.fifo_enQueue)
    i.fifo_queueLength                       0x08000c24   Section        0  fifo.o(i.fifo_queueLength)
    i.main                                   0x08000c60   Section        0  main.o(i.main)
    i.nvic_init                              0x08000d3c   Section        0  board.o(i.nvic_init)
    i.usart_SendByte                         0x08000d64   Section        0  usart.o(i.usart_SendByte)
    i.usart_SendCmd                          0x08000da0   Section        0  usart.o(i.usart_SendCmd)
    i.usart_init                             0x08000dcc   Section        0  board.o(i.usart_init)
    .data                                    0x20000000   Section        8  main.o(.data)
    .data                                    0x20000008   Section        2  usart.o(.data)
    .data                                    0x2000000c   Section       20  system_stm32f10x.o(.data)
    .data                                    0x20000020   Section       20  stm32f10x_rcc.o(.data)
    APBAHBPrescTable                         0x20000020   Data          16  stm32f10x_rcc.o(.data)
    ADCPrescTable                            0x20000030   Data           4  stm32f10x_rcc.o(.data)
    .bss                                     0x20000034   Section      128  usart.o(.bss)
    .bss                                     0x200000b4   Section      258  fifo.o(.bss)
    STACK                                    0x200001b8   Section     2048  startup_stm32f10x_hd.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$P$D$K$B$S$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$EBA8$MICROLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __decompress                              - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _microlib_exit                            - Undefined Weak Reference
    __Vectors_Size                           0x00000130   Number         0  startup_stm32f10x_hd.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f10x_hd.o(RESET)
    __Vectors_End                            0x08000130   Data           0  startup_stm32f10x_hd.o(RESET)
    __main                                   0x08000131   Thumb Code     0  entry.o(.ARM.Collect$$$$00000000)
    _main_stk                                0x08000131   Thumb Code     0  entry2.o(.ARM.Collect$$$$00000001)
    _main_scatterload                        0x08000135   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    __main_after_scatterload                 0x08000139   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    _main_clock                              0x08000139   Thumb Code     0  entry7b.o(.ARM.Collect$$$$00000008)
    _main_cpp_init                           0x08000139   Thumb Code     0  entry8b.o(.ARM.Collect$$$$0000000A)
    _main_init                               0x08000139   Thumb Code     0  entry9a.o(.ARM.Collect$$$$0000000B)
    __rt_final_cpp                           0x08000141   Thumb Code     0  entry10a.o(.ARM.Collect$$$$0000000D)
    __rt_final_exit                          0x08000141   Thumb Code     0  entry11a.o(.ARM.Collect$$$$0000000F)
    Reset_Handler                            0x08000145   Thumb Code     8  startup_stm32f10x_hd.o(.text)
    ADC1_2_IRQHandler                        0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    ADC3_IRQHandler                          0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    CAN1_RX1_IRQHandler                      0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    CAN1_SCE_IRQHandler                      0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel1_IRQHandler                 0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel2_IRQHandler                 0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel3_IRQHandler                 0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel4_IRQHandler                 0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel5_IRQHandler                 0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel6_IRQHandler                 0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel7_IRQHandler                 0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA2_Channel1_IRQHandler                 0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA2_Channel2_IRQHandler                 0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA2_Channel3_IRQHandler                 0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA2_Channel4_5_IRQHandler               0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI0_IRQHandler                         0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI15_10_IRQHandler                     0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI1_IRQHandler                         0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI2_IRQHandler                         0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI3_IRQHandler                         0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI4_IRQHandler                         0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI9_5_IRQHandler                       0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    FLASH_IRQHandler                         0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    FSMC_IRQHandler                          0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    I2C1_ER_IRQHandler                       0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    I2C1_EV_IRQHandler                       0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    I2C2_ER_IRQHandler                       0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    I2C2_EV_IRQHandler                       0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    PVD_IRQHandler                           0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    RCC_IRQHandler                           0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    RTCAlarm_IRQHandler                      0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    RTC_IRQHandler                           0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    SDIO_IRQHandler                          0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    SPI1_IRQHandler                          0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    SPI2_IRQHandler                          0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    SPI3_IRQHandler                          0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TAMPER_IRQHandler                        0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM1_BRK_IRQHandler                      0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM1_CC_IRQHandler                       0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM1_TRG_COM_IRQHandler                  0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM1_UP_IRQHandler                       0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM2_IRQHandler                          0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM3_IRQHandler                          0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM4_IRQHandler                          0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM5_IRQHandler                          0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM6_IRQHandler                          0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM7_IRQHandler                          0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM8_BRK_IRQHandler                      0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM8_CC_IRQHandler                       0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM8_TRG_COM_IRQHandler                  0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM8_UP_IRQHandler                       0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    UART4_IRQHandler                         0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    UART5_IRQHandler                         0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USART2_IRQHandler                        0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USART3_IRQHandler                        0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USBWakeUp_IRQHandler                     0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USB_HP_CAN1_TX_IRQHandler                0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USB_LP_CAN1_RX0_IRQHandler               0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    WWDG_IRQHandler                          0x0800015f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    __aeabi_fmul                             0x08000169   Thumb Code   100  fmul.o(.text)
    __aeabi_fdiv                             0x080001cd   Thumb Code   124  fdiv.o(.text)
    __aeabi_ui2f                             0x08000249   Thumb Code    10  ffltui.o(.text)
    __I$use$fp                               0x08000253   Thumb Code     0  iusefp.o(.text)
    _float_round                             0x08000253   Thumb Code    18  fepilogue.o(.text)
    _float_epilogue                          0x08000265   Thumb Code    92  fepilogue.o(.text)
    __scatterload                            0x080002c1   Thumb Code    28  init.o(.text)
    __scatterload_rt2                        0x080002c1   Thumb Code     0  init.o(.text)
    BusFault_Handler                         0x080002e5   Thumb Code     4  stm32f10x_it.o(i.BusFault_Handler)
    DebugMon_Handler                         0x080002e9   Thumb Code     2  stm32f10x_it.o(i.DebugMon_Handler)
    Emm_V5_Pos_Control                       0x080002eb   Thumb Code   108  emm_v5.o(i.Emm_V5_Pos_Control)
    Emm_V5_Read_Sys_Params                   0x08000357   Thumb Code   262  emm_v5.o(i.Emm_V5_Read_Sys_Params)
    GPIO_Init                                0x0800045d   Thumb Code   278  stm32f10x_gpio.o(i.GPIO_Init)
    GPIO_PinRemapConfig                      0x08000575   Thumb Code   138  stm32f10x_gpio.o(i.GPIO_PinRemapConfig)
    HardFault_Handler                        0x08000605   Thumb Code     4  stm32f10x_it.o(i.HardFault_Handler)
    MemManage_Handler                        0x08000609   Thumb Code     4  stm32f10x_it.o(i.MemManage_Handler)
    NMI_Handler                              0x0800060d   Thumb Code     2  stm32f10x_it.o(i.NMI_Handler)
    NVIC_Init                                0x08000611   Thumb Code   100  misc.o(i.NVIC_Init)
    NVIC_PriorityGroupConfig                 0x08000681   Thumb Code    10  misc.o(i.NVIC_PriorityGroupConfig)
    PendSV_Handler                           0x08000695   Thumb Code     2  stm32f10x_it.o(i.PendSV_Handler)
    RCC_APB2PeriphClockCmd                   0x08000699   Thumb Code    26  stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd)
    RCC_GetClocksFreq                        0x080006b9   Thumb Code   192  stm32f10x_rcc.o(i.RCC_GetClocksFreq)
    SVC_Handler                              0x0800078d   Thumb Code     2  stm32f10x_it.o(i.SVC_Handler)
    SysTick_Handler                          0x08000879   Thumb Code     2  stm32f10x_it.o(i.SysTick_Handler)
    SystemInit                               0x0800087d   Thumb Code    78  system_stm32f10x.o(i.SystemInit)
    USART1_IRQHandler                        0x080008dd   Thumb Code   120  usart.o(i.USART1_IRQHandler)
    USART_ClearITPendingBit                  0x08000965   Thumb Code    30  stm32f10x_usart.o(i.USART_ClearITPendingBit)
    USART_Cmd                                0x08000983   Thumb Code    24  stm32f10x_usart.o(i.USART_Cmd)
    USART_GetITStatus                        0x0800099b   Thumb Code    84  stm32f10x_usart.o(i.USART_GetITStatus)
    USART_ITConfig                           0x080009ef   Thumb Code    74  stm32f10x_usart.o(i.USART_ITConfig)
    USART_Init                               0x08000a39   Thumb Code   210  stm32f10x_usart.o(i.USART_Init)
    UsageFault_Handler                       0x08000b11   Thumb Code     4  stm32f10x_it.o(i.UsageFault_Handler)
    __scatterload_copy                       0x08000b15   Thumb Code    14  handlers.o(i.__scatterload_copy)
    __scatterload_null                       0x08000b23   Thumb Code     2  handlers.o(i.__scatterload_null)
    __scatterload_zeroinit                   0x08000b25   Thumb Code    14  handlers.o(i.__scatterload_zeroinit)
    board_init                               0x08000b33   Thumb Code    16  board.o(i.board_init)
    clock_init                               0x08000b45   Thumb Code    28  board.o(i.clock_init)
    delay_ms                                 0x08000b65   Thumb Code    90  delay.o(i.delay_ms)
    fifo_deQueue                             0x08000bc5   Thumb Code    44  fifo.o(i.fifo_deQueue)
    fifo_enQueue                             0x08000bf5   Thumb Code    42  fifo.o(i.fifo_enQueue)
    fifo_queueLength                         0x08000c25   Thumb Code    56  fifo.o(i.fifo_queueLength)
    main                                     0x08000c61   Thumb Code   194  main.o(i.main)
    nvic_init                                0x08000d3d   Thumb Code    40  board.o(i.nvic_init)
    usart_SendByte                           0x08000d65   Thumb Code    56  usart.o(i.usart_SendByte)
    usart_SendCmd                            0x08000da1   Thumb Code    44  usart.o(i.usart_SendCmd)
    usart_init                               0x08000dcd   Thumb Code   148  board.o(i.usart_init)
    Region$$Table$$Base                      0x08000e68   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x08000e88   Number         0  anon$$obj.o(Region$$Table)
    pos                                      0x20000000   Data           4  main.o(.data)
    Motor_Cur_Pos                            0x20000004   Data           4  main.o(.data)
    rxFrameFlag                              0x20000008   Data           1  usart.o(.data)
    rxCount                                  0x20000009   Data           1  usart.o(.data)
    SystemCoreClock                          0x2000000c   Data           4  system_stm32f10x.o(.data)
    AHBPrescTable                            0x20000010   Data          16  system_stm32f10x.o(.data)
    rxCmd                                    0x20000034   Data         128  usart.o(.bss)
    rxFIFO                                   0x200000b4   Data         258  fifo.o(.bss)
    __initial_sp                             0x200009b8   Data           0  startup_stm32f10x_hd.o(STACK)



==============================================================================

Memory Map of the image

  Image Entry point : 0x08000131

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x00000ebc, Max: 0x00010000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x00000e88, Max: 0x00010000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x00000130   Data   RO          288    RESET               startup_stm32f10x_hd.o
    0x08000130   0x08000130   0x00000000   Code   RO          917  * .ARM.Collect$$$$00000000  mc_w.l(entry.o)
    0x08000130   0x08000130   0x00000004   Code   RO          928    .ARM.Collect$$$$00000001  mc_w.l(entry2.o)
    0x08000134   0x08000134   0x00000004   Code   RO          931    .ARM.Collect$$$$00000004  mc_w.l(entry5.o)
    0x08000138   0x08000138   0x00000000   Code   RO          933    .ARM.Collect$$$$00000008  mc_w.l(entry7b.o)
    0x08000138   0x08000138   0x00000000   Code   RO          935    .ARM.Collect$$$$0000000A  mc_w.l(entry8b.o)
    0x08000138   0x08000138   0x00000008   Code   RO          936    .ARM.Collect$$$$0000000B  mc_w.l(entry9a.o)
    0x08000140   0x08000140   0x00000000   Code   RO          938    .ARM.Collect$$$$0000000D  mc_w.l(entry10a.o)
    0x08000140   0x08000140   0x00000000   Code   RO          940    .ARM.Collect$$$$0000000F  mc_w.l(entry11a.o)
    0x08000140   0x08000140   0x00000004   Code   RO          929    .ARM.Collect$$$$00002712  mc_w.l(entry2.o)
    0x08000144   0x08000144   0x00000024   Code   RO          289    .text               startup_stm32f10x_hd.o
    0x08000168   0x08000168   0x00000064   Code   RO          922    .text               mf_w.l(fmul.o)
    0x080001cc   0x080001cc   0x0000007c   Code   RO          924    .text               mf_w.l(fdiv.o)
    0x08000248   0x08000248   0x0000000a   Code   RO          926    .text               mf_w.l(ffltui.o)
    0x08000252   0x08000252   0x00000000   Code   RO          942    .text               mc_w.l(iusefp.o)
    0x08000252   0x08000252   0x0000006e   Code   RO          943    .text               mf_w.l(fepilogue.o)
    0x080002c0   0x080002c0   0x00000024   Code   RO          945    .text               mc_w.l(init.o)
    0x080002e4   0x080002e4   0x00000004   Code   RO           67    i.BusFault_Handler  stm32f10x_it.o
    0x080002e8   0x080002e8   0x00000002   Code   RO           68    i.DebugMon_Handler  stm32f10x_it.o
    0x080002ea   0x080002ea   0x0000006c   Code   RO          136    i.Emm_V5_Pos_Control  emm_v5.o
    0x08000356   0x08000356   0x00000106   Code   RO          137    i.Emm_V5_Read_Sys_Params  emm_v5.o
    0x0800045c   0x0800045c   0x00000116   Code   RO          393    i.GPIO_Init         stm32f10x_gpio.o
    0x08000572   0x08000572   0x00000002   PAD
    0x08000574   0x08000574   0x00000090   Code   RO          395    i.GPIO_PinRemapConfig  stm32f10x_gpio.o
    0x08000604   0x08000604   0x00000004   Code   RO           69    i.HardFault_Handler  stm32f10x_it.o
    0x08000608   0x08000608   0x00000004   Code   RO           70    i.MemManage_Handler  stm32f10x_it.o
    0x0800060c   0x0800060c   0x00000002   Code   RO           71    i.NMI_Handler       stm32f10x_it.o
    0x0800060e   0x0800060e   0x00000002   PAD
    0x08000610   0x08000610   0x00000070   Code   RO          881    i.NVIC_Init         misc.o
    0x08000680   0x08000680   0x00000014   Code   RO          882    i.NVIC_PriorityGroupConfig  misc.o
    0x08000694   0x08000694   0x00000002   Code   RO           72    i.PendSV_Handler    stm32f10x_it.o
    0x08000696   0x08000696   0x00000002   PAD
    0x08000698   0x08000698   0x00000020   Code   RO          505    i.RCC_APB2PeriphClockCmd  stm32f10x_rcc.o
    0x080006b8   0x080006b8   0x000000d4   Code   RO          513    i.RCC_GetClocksFreq  stm32f10x_rcc.o
    0x0800078c   0x0800078c   0x00000002   Code   RO           73    i.SVC_Handler       stm32f10x_it.o
    0x0800078e   0x0800078e   0x00000008   Code   RO          293    i.SetSysClock       system_stm32f10x.o
    0x08000796   0x08000796   0x00000002   PAD
    0x08000798   0x08000798   0x000000e0   Code   RO          294    i.SetSysClockTo72   system_stm32f10x.o
    0x08000878   0x08000878   0x00000002   Code   RO           74    i.SysTick_Handler   stm32f10x_it.o
    0x0800087a   0x0800087a   0x00000002   PAD
    0x0800087c   0x0800087c   0x00000060   Code   RO          296    i.SystemInit        system_stm32f10x.o
    0x080008dc   0x080008dc   0x00000088   Code   RO          214    i.USART1_IRQHandler  usart.o
    0x08000964   0x08000964   0x0000001e   Code   RO          702    i.USART_ClearITPendingBit  stm32f10x_usart.o
    0x08000982   0x08000982   0x00000018   Code   RO          705    i.USART_Cmd         stm32f10x_usart.o
    0x0800099a   0x0800099a   0x00000054   Code   RO          709    i.USART_GetITStatus  stm32f10x_usart.o
    0x080009ee   0x080009ee   0x0000004a   Code   RO          711    i.USART_ITConfig    stm32f10x_usart.o
    0x08000a38   0x08000a38   0x000000d8   Code   RO          712    i.USART_Init        stm32f10x_usart.o
    0x08000b10   0x08000b10   0x00000004   Code   RO           75    i.UsageFault_Handler  stm32f10x_it.o
    0x08000b14   0x08000b14   0x0000000e   Code   RO          949    i.__scatterload_copy  mc_w.l(handlers.o)
    0x08000b22   0x08000b22   0x00000002   Code   RO          950    i.__scatterload_null  mc_w.l(handlers.o)
    0x08000b24   0x08000b24   0x0000000e   Code   RO          951    i.__scatterload_zeroinit  mc_w.l(handlers.o)
    0x08000b32   0x08000b32   0x00000010   Code   RO          245    i.board_init        board.o
    0x08000b42   0x08000b42   0x00000002   PAD
    0x08000b44   0x08000b44   0x00000020   Code   RO          246    i.clock_init        board.o
    0x08000b64   0x08000b64   0x00000060   Code   RO          370    i.delay_ms          delay.o
    0x08000bc4   0x08000bc4   0x00000030   Code   RO          327    i.fifo_deQueue      fifo.o
    0x08000bf4   0x08000bf4   0x00000030   Code   RO          328    i.fifo_enQueue      fifo.o
    0x08000c24   0x08000c24   0x0000003c   Code   RO          330    i.fifo_queueLength  fifo.o
    0x08000c60   0x08000c60   0x000000dc   Code   RO            1    i.main              main.o
    0x08000d3c   0x08000d3c   0x00000028   Code   RO          247    i.nvic_init         board.o
    0x08000d64   0x08000d64   0x0000003c   Code   RO          215    i.usart_SendByte    usart.o
    0x08000da0   0x08000da0   0x0000002c   Code   RO          216    i.usart_SendCmd     usart.o
    0x08000dcc   0x08000dcc   0x0000009c   Code   RO          248    i.usart_init        board.o
    0x08000e68   0x08000e68   0x00000020   Data   RO          947    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x08000e88, Size: 0x000009b8, Max: 0x00005000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x08000e88   0x00000008   Data   RW            2    .data               main.o
    0x20000008   0x08000e90   0x00000002   Data   RW          218    .data               usart.o
    0x2000000a   0x08000e92   0x00000002   PAD
    0x2000000c   0x08000e94   0x00000014   Data   RW          297    .data               system_stm32f10x.o
    0x20000020   0x08000ea8   0x00000014   Data   RW          533    .data               stm32f10x_rcc.o
    0x20000034        -       0x00000080   Zero   RW          217    .bss                usart.o
    0x200000b4        -       0x00000102   Zero   RW          332    .bss                fifo.o
    0x200001b6   0x08000ebc   0x00000002   PAD
    0x200001b8        -       0x00000800   Zero   RW          286    STACK               startup_stm32f10x_hd.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

       244         12          0          0          0       2291   board.o
         0          0          0          0          0         32   core_cm3.o
        96          6          0          0          0       1111   delay.o
       370         18          0          0          0       1862   emm_v5.o
       156         14          0          0        258       2946   fifo.o
       220         26          0          8          0     233167   main.o
       132         22          0          0          0       2047   misc.o
        36          8        304          0       2048        848   startup_stm32f10x_hd.o
       422          6          0          0          0       3306   stm32f10x_gpio.o
        26          0          0          0          0       4618   stm32f10x_it.o
       244         26          0         20          0       4477   stm32f10x_rcc.o
       428          6          0          0          0       5964   stm32f10x_usart.o
       328         28          0         20          0       2725   system_stm32f10x.o
       240         20          0          2        128       2765   usart.o

    ----------------------------------------------------------------------
      2954        <USER>        <GROUP>         52       2436     268159   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        12          0          0          2          2          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

         0          0          0          0          0          0   entry.o
         0          0          0          0          0          0   entry10a.o
         0          0          0          0          0          0   entry11a.o
         8          4          0          0          0          0   entry2.o
         4          0          0          0          0          0   entry5.o
         0          0          0          0          0          0   entry7b.o
         0          0          0          0          0          0   entry8b.o
         8          4          0          0          0          0   entry9a.o
        30          0          0          0          0          0   handlers.o
        36          8          0          0          0         68   init.o
         0          0          0          0          0          0   iusefp.o
       124          0          0          0          0         88   fdiv.o
       110          0          0          0          0        168   fepilogue.o
        10          0          0          0          0         68   ffltui.o
       100          0          0          0          0         76   fmul.o

    ----------------------------------------------------------------------
       430         <USER>          <GROUP>          0          0        468   Library Totals
         0          0          0          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

        86         16          0          0          0         68   mc_w.l
       344          0          0          0          0        400   mf_w.l

    ----------------------------------------------------------------------
       430         <USER>          <GROUP>          0          0        468   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

      3384        208        336         52       2436     266907   Grand Totals
      3384        208        336         52       2436     266907   ELF Image Totals
      3384        208        336         52          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                 3720 (   3.63kB)
    Total RW  Size (RW Data + ZI Data)              2488 (   2.43kB)
    Total ROM Size (Code + RO Data + RW Data)       3772 (   3.68kB)

==============================================================================

