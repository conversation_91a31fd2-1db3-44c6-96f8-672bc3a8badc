#include "emm_v5_hal.h"
#include <string.h>

/**********************************************************
*** Emm_V5.0闭环步进电机驱动库 - HAL库移植版本
*** 原作者：ZHANGDATOU  
*** HAL移植：Assistant
*** 支持平台：STM32F1xx HAL库
**********************************************************/

// 私有函数声明
static void EMM_V5_SendCmd(EMM_V5_HandleTypeDef *hemm, uint8_t *cmd, uint8_t len);

/**
 * @brief    初始化EMM_V5驱动
 * @param    hemm  EMM_V5句柄
 * @param    huart UART句柄
 * @retval   无
 */
void EMM_V5_Init(EMM_V5_HandleTypeDef *hemm, UART_HandleTypeDef *huart)
{
    hemm->huart = huart;
    hemm->rx_frame_flag = false;
    hemm->rx_count = 0;
    hemm->last_tx_time = 0;
    
    // 清空缓冲区
    memset((void*)hemm->rx_buffer, 0, EMM_V5_RX_BUFFER_SIZE);
    memset((void*)hemm->tx_buffer, 0, EMM_V5_TX_BUFFER_SIZE);
    memset((void*)hemm->rx_cmd, 0, EMM_V5_RX_BUFFER_SIZE);
    
    // 启动UART接收中断
    HAL_UART_Receive_IT(hemm->huart, hemm->rx_buffer, 1);
}

/**
 * @brief    UART接收完成回调函数
 * @param    hemm  EMM_V5句柄
 * @retval   无
 */
void EMM_V5_UART_RxCpltCallback(EMM_V5_HandleTypeDef *hemm)
{
    // 将接收到的数据存入命令缓冲区
    if (hemm->rx_count < EMM_V5_RX_BUFFER_SIZE - 1) {
        hemm->rx_cmd[hemm->rx_count++] = hemm->rx_buffer[0];
    }
    
    // 重新启动接收
    HAL_UART_Receive_IT(hemm->huart, hemm->rx_buffer, 1);
}

/**
 * @brief    UART空闲中断回调函数
 * @param    hemm  EMM_V5句柄
 * @retval   无
 */
void EMM_V5_UART_IdleCallback(EMM_V5_HandleTypeDef *hemm)
{
    // 一帧数据接收完成，设置帧标志位
    hemm->rx_frame_flag = true;
}

/**
 * @brief    发送命令到电机
 * @param    hemm  EMM_V5句柄
 * @param    cmd   命令数组
 * @param    len   命令长度
 * @retval   无
 */
static void EMM_V5_SendCmd(EMM_V5_HandleTypeDef *hemm, uint8_t *cmd, uint8_t len)
{
    // 复制命令到发送缓冲区
    memcpy(hemm->tx_buffer, cmd, len);
    
    // 发送命令
    HAL_UART_Transmit(hemm->huart, hemm->tx_buffer, len, EMM_V5_CMD_TIMEOUT);
    
    // 记录发送时间
    hemm->last_tx_time = HAL_GetTick();
    
    // 清空接收标志和计数
    hemm->rx_frame_flag = false;
    hemm->rx_count = 0;
}

/**
 * @brief    等待电机响应
 * @param    hemm     EMM_V5句柄
 * @param    timeout  超时时间(ms)
 * @retval   true-收到响应, false-超时
 */
bool EMM_V5_Wait_Response(EMM_V5_HandleTypeDef *hemm, uint32_t timeout)
{
    uint32_t start_time = HAL_GetTick();
    
    while (!hemm->rx_frame_flag) {
        if ((HAL_GetTick() - start_time) > timeout) {
            return false;  // 超时
        }
        HAL_Delay(1);
    }
    
    return true;  // 收到响应
}

/**
 * @brief    检查响应是否正确
 * @param    hemm  EMM_V5句柄
 * @param    addr  电机地址
 * @param    cmd   命令码
 * @retval   true-响应正确, false-响应错误
 */
bool EMM_V5_Check_Response(EMM_V5_HandleTypeDef *hemm, uint8_t addr, uint8_t cmd)
{
    if (hemm->rx_count < 4) return false;
    
    return (hemm->rx_cmd[0] == addr && hemm->rx_cmd[1] == cmd);
}

/**
 * @brief    解析位置数据
 * @param    hemm  EMM_V5句柄
 * @param    addr  电机地址
 * @retval   位置角度值
 */
float EMM_V5_Parse_Position(EMM_V5_HandleTypeDef *hemm, uint8_t addr)
{
    float pos = 0.0f;
    
    // 校验地址、命令码、数据长度
    if (hemm->rx_cmd[0] == addr && hemm->rx_cmd[1] == 0x36 && hemm->rx_count == 8) {
        // 拼接成uint32_t数据
        uint32_t raw_pos = ((uint32_t)hemm->rx_cmd[3] << 24) |
                          ((uint32_t)hemm->rx_cmd[4] << 16) |
                          ((uint32_t)hemm->rx_cmd[5] << 8)  |
                          ((uint32_t)hemm->rx_cmd[6] << 0);
        
        // 转换成角度
        pos = (float)raw_pos * 360.0f / 65536.0f;
        
        // 处理方向
        if (hemm->rx_cmd[2]) {
            pos = -pos;
        }
    }
    
    return pos;
}

/**
 * @brief    复位当前位置到零点
 * @param    hemm  EMM_V5句柄
 * @param    addr  电机地址
 * @retval   无
 */
void EMM_V5_Reset_CurPos_To_Zero(EMM_V5_HandleTypeDef *hemm, uint8_t addr)
{
    uint8_t cmd[4] = {0};
    
    // 装载命令
    cmd[0] = addr;      // 地址
    cmd[1] = 0x0A;      // 功能码
    cmd[2] = 0x6D;      // 辅助码
    cmd[3] = 0x6B;      // 校验字节
    
    // 发送命令
    EMM_V5_SendCmd(hemm, cmd, 4);
}

/**
 * @brief    清除堵转保护
 * @param    hemm  EMM_V5句柄
 * @param    addr  电机地址
 * @retval   无
 */
void EMM_V5_Reset_Clog_Pro(EMM_V5_HandleTypeDef *hemm, uint8_t addr)
{
    uint8_t cmd[4] = {0};
    
    // 装载命令
    cmd[0] = addr;      // 地址
    cmd[1] = 0x0E;      // 功能码
    cmd[2] = 0x52;      // 辅助码
    cmd[3] = 0x6B;      // 校验字节
    
    // 发送命令
    EMM_V5_SendCmd(hemm, cmd, 4);
}

/**
 * @brief    使能信号控制
 * @param    hemm   EMM_V5句柄
 * @param    addr   电机地址
 * @param    state  使能状态 (true为使能电机，false为关闭电机)
 * @param    snF    多机同步标志 (false为不启用，true为启用)
 * @retval   无
 */
void EMM_V5_En_Control(EMM_V5_HandleTypeDef *hemm, uint8_t addr, bool state, bool snF)
{
    uint8_t cmd[6] = {0};
    
    // 装载命令
    cmd[0] = addr;              // 地址
    cmd[1] = 0xF3;              // 功能码
    cmd[2] = 0xAB;              // 辅助码
    cmd[3] = (uint8_t)state;    // 使能状态
    cmd[4] = snF;               // 多机同步运动标志
    cmd[5] = 0x6B;              // 校验字节
    
    // 发送命令
    EMM_V5_SendCmd(hemm, cmd, 6);
}

/**
 * @brief    速度模式控制
 * @param    hemm  EMM_V5句柄
 * @param    addr  电机地址
 * @param    dir   方向 (0为CW，非零值为CCW)
 * @param    vel   目标速度 (范围0-5000RPM)
 * @param    acc   加速度 (范围0-255，注意：0表示直接启动)
 * @param    snF   多机同步标志 (false为不启用，true为启用)
 * @retval   无
 */
void EMM_V5_Vel_Control(EMM_V5_HandleTypeDef *hemm, uint8_t addr, uint8_t dir, uint16_t vel, uint8_t acc, bool snF)
{
    uint8_t cmd[8] = {0};
    
    // 装载命令
    cmd[0] = addr;                      // 地址
    cmd[1] = 0xF6;                      // 功能码
    cmd[2] = dir;                       // 方向
    cmd[3] = (uint8_t)(vel >> 8);       // 速度(RPM)高8位字节
    cmd[4] = (uint8_t)(vel >> 0);       // 速度(RPM)低8位字节
    cmd[5] = acc;                       // 加速度，注意：0表示直接启动
    cmd[6] = snF;                       // 多机同步运动标志
    cmd[7] = 0x6B;                      // 校验字节
    
    // 发送命令
    EMM_V5_SendCmd(hemm, cmd, 8);
}

/**
 * @brief    位置模式控制
 * @param    hemm  EMM_V5句柄
 * @param    addr  电机地址
 * @param    dir   方向 (0为CW，非零值为CCW)
 * @param    vel   目标速度(RPM) (范围0-5000RPM)
 * @param    acc   加速度 (范围0-255，注意：0表示直接启动)
 * @param    clk   脉冲数量 (范围0-(2^32-1))
 * @param    raF   相对位/绝对位标志 (false为相对运动，true为绝对值运动)
 * @param    snF   多机同步标志 (false为不启用，true为启用)
 * @retval   无
 */
void EMM_V5_Pos_Control(EMM_V5_HandleTypeDef *hemm, uint8_t addr, uint8_t dir, uint16_t vel, uint8_t acc, uint32_t clk, bool raF, bool snF)
{
    uint8_t cmd[13] = {0};
    
    // 装载命令
    cmd[0]  = addr;                     // 地址
    cmd[1]  = 0xFD;                     // 功能码
    cmd[2]  = dir;                      // 方向
    cmd[3]  = (uint8_t)(vel >> 8);      // 速度(RPM)高8位字节
    cmd[4]  = (uint8_t)(vel >> 0);      // 速度(RPM)低8位字节
    cmd[5]  = acc;                      // 加速度，注意：0表示直接启动
    cmd[6]  = (uint8_t)(clk >> 24);     // 脉冲数(bit24-bit31)
    cmd[7]  = (uint8_t)(clk >> 16);     // 脉冲数(bit16-bit23)
    cmd[8]  = (uint8_t)(clk >> 8);      // 脉冲数(bit8-bit15)
    cmd[9]  = (uint8_t)(clk >> 0);      // 脉冲数(bit0-bit7)
    cmd[10] = raF;                      // 相对位/绝对位标志 (false为相对运动，true为绝对值运动)
    cmd[11] = snF;                      // 多机同步运动标志 (false为不启用，true为启用)
    cmd[12] = 0x6B;                     // 校验字节
    
    // 发送命令
    EMM_V5_SendCmd(hemm, cmd, 13);
}

/**
 * @brief    读取系统参数
 * @param    hemm  EMM_V5句柄
 * @param    addr  电机地址
 * @param    s     系统参数类型
 * @retval   无
 */
void EMM_V5_Read_Sys_Params(EMM_V5_HandleTypeDef *hemm, uint8_t addr, SysParams_t s)
{
    uint8_t i = 0;
    uint8_t cmd[16] = {0};

    // 装载命令
    cmd[i] = addr; ++i;                 // 地址

    switch(s)                           // 功能码
    {
        case S_VER  : cmd[i] = 0x1F; ++i; break;
        case S_RL   : cmd[i] = 0x20; ++i; break;
        case S_PID  : cmd[i] = 0x21; ++i; break;
        case S_VBUS : cmd[i] = 0x24; ++i; break;
        case S_CPHA : cmd[i] = 0x27; ++i; break;
        case S_ENCL : cmd[i] = 0x31; ++i; break;
        case S_TPOS : cmd[i] = 0x33; ++i; break;
        case S_VEL  : cmd[i] = 0x35; ++i; break;
        case S_CPOS : cmd[i] = 0x36; ++i; break;
        case S_PERR : cmd[i] = 0x37; ++i; break;
        case S_FLAG : cmd[i] = 0x3A; ++i; break;
        case S_ORG  : cmd[i] = 0x3B; ++i; break;
        case S_Conf : cmd[i] = 0x42; ++i; cmd[i] = 0x6C; ++i; break;
        case S_State: cmd[i] = 0x43; ++i; cmd[i] = 0x7A; ++i; break;
        default: break;
    }

    cmd[i] = 0x6B; ++i;                 // 校验字节

    // 发送命令
    EMM_V5_SendCmd(hemm, cmd, i);
}

/**
 * @brief    修改控制/闭环模式
 * @param    hemm       EMM_V5句柄
 * @param    addr       电机地址
 * @param    svF        是否存储标志 (false为不存储，true为存储)
 * @param    ctrl_mode  控制模式
 * @retval   无
 */
void EMM_V5_Modify_Ctrl_Mode(EMM_V5_HandleTypeDef *hemm, uint8_t addr, bool svF, uint8_t ctrl_mode)
{
    uint8_t cmd[6] = {0};

    // 装载命令
    cmd[0] = addr;          // 地址
    cmd[1] = 0x46;          // 功能码
    cmd[2] = 0x69;          // 辅助码
    cmd[3] = svF;           // 是否存储标志 (false为不存储，true为存储)
    cmd[4] = ctrl_mode;     // 控制模式
    cmd[5] = 0x6B;          // 校验字节

    // 发送命令
    EMM_V5_SendCmd(hemm, cmd, 6);
}

/**
 * @brief    立即停止（所有控制模式通用）
 * @param    hemm  EMM_V5句柄
 * @param    addr  电机地址
 * @param    snF   多机同步标志 (false为不启用，true为启用)
 * @retval   无
 */
void EMM_V5_Stop_Now(EMM_V5_HandleTypeDef *hemm, uint8_t addr, bool snF)
{
    uint8_t cmd[5] = {0};

    // 装载命令
    cmd[0] = addr;      // 地址
    cmd[1] = 0xFE;      // 功能码
    cmd[2] = 0x98;      // 辅助码
    cmd[3] = snF;       // 多机同步运动标志
    cmd[4] = 0x6B;      // 校验字节

    // 发送命令
    EMM_V5_SendCmd(hemm, cmd, 5);
}

/**
 * @brief    多机同步运动
 * @param    hemm  EMM_V5句柄
 * @param    addr  电机地址
 * @retval   无
 */
void EMM_V5_Synchronous_motion(EMM_V5_HandleTypeDef *hemm, uint8_t addr)
{
    uint8_t cmd[4] = {0};

    // 装载命令
    cmd[0] = addr;      // 地址
    cmd[1] = 0xFF;      // 功能码
    cmd[2] = 0x66;      // 辅助码
    cmd[3] = 0x6B;      // 校验字节

    // 发送命令
    EMM_V5_SendCmd(hemm, cmd, 4);
}

/**
 * @brief    设置当前圆圈位置为零点
 * @param    hemm  EMM_V5句柄
 * @param    addr  电机地址
 * @param    svF   是否存储标志 (false为不存储，true为存储)
 * @retval   无
 */
void EMM_V5_Origin_Set_O(EMM_V5_HandleTypeDef *hemm, uint8_t addr, bool svF)
{
    uint8_t cmd[5] = {0};

    // 装载命令
    cmd[0] = addr;      // 地址
    cmd[1] = 0x93;      // 功能码
    cmd[2] = 0x88;      // 辅助码
    cmd[3] = svF;       // 是否存储标志 (false为不存储，true为存储)
    cmd[4] = 0x6B;      // 校验字节

    // 发送命令
    EMM_V5_SendCmd(hemm, cmd, 5);
}

/**
 * @brief    触发回零
 * @param    hemm    EMM_V5句柄
 * @param    addr    电机地址
 * @param    o_mode  回零模式
 * @param    snF     多机同步标志 (false为不启用，true为启用)
 * @retval   无
 */
void EMM_V5_Origin_Trigger_Return(EMM_V5_HandleTypeDef *hemm, uint8_t addr, uint8_t o_mode, bool snF)
{
    uint8_t cmd[5] = {0};

    // 装载命令
    cmd[0] = addr;      // 地址
    cmd[1] = 0x9A;      // 功能码
    cmd[2] = o_mode;    // 回零模式
    cmd[3] = snF;       // 多机同步运动标志 (false为不启用，true为启用)
    cmd[4] = 0x6B;      // 校验字节

    // 发送命令
    EMM_V5_SendCmd(hemm, cmd, 5);
}

/**
 * @brief    强制中断并退出回零
 * @param    hemm  EMM_V5句柄
 * @param    addr  电机地址
 * @retval   无
 */
void EMM_V5_Origin_Interrupt(EMM_V5_HandleTypeDef *hemm, uint8_t addr)
{
    uint8_t cmd[4] = {0};

    // 装载命令
    cmd[0] = addr;      // 地址
    cmd[1] = 0x9C;      // 功能码
    cmd[2] = 0x48;      // 辅助码
    cmd[3] = 0x6B;      // 校验字节

    // 发送命令
    EMM_V5_SendCmd(hemm, cmd, 4);
}

/**
 * @brief    修改回零参数
 * @param    hemm    EMM_V5句柄
 * @param    addr    电机地址
 * @param    svF     是否存储标志 (false为不存储，true为存储)
 * @param    o_mode  回零模式 (0为单圈就近回零，1为单圈方向回零，2为单圈限位碰撞回零，3为单圈限位缓慢回零)
 * @param    o_dir   回零方向 (0为CW，非零值为CCW)
 * @param    o_vel   回零速度，单位：RPM（转/分钟）
 * @param    o_tm    回零超时时间，单位：毫秒
 * @param    sl_vel  碰撞限位检测转速，单位：RPM（转/分钟）
 * @param    sl_ma   碰撞限位检测电流，单位：Ma（毫安）
 * @param    sl_ms   碰撞限位检测时间，单位：Ms（毫秒）
 * @param    potF    上电自动触发回零，false为不使能，true为使能
 * @retval   无
 */
void EMM_V5_Origin_Modify_Params(EMM_V5_HandleTypeDef *hemm, uint8_t addr, bool svF, uint8_t o_mode, uint8_t o_dir,
                                uint16_t o_vel, uint32_t o_tm, uint16_t sl_vel, uint16_t sl_ma, uint16_t sl_ms, bool potF)
{
    uint8_t cmd[20] = {0};

    // 装载命令
    cmd[0]  = addr;                     // 地址
    cmd[1]  = 0x4C;                     // 功能码
    cmd[2]  = 0xAE;                     // 辅助码
    cmd[3]  = svF;                      // 是否存储标志 (false为不存储，true为存储)
    cmd[4]  = o_mode;                   // 回零模式
    cmd[5]  = o_dir;                    // 回零方向
    cmd[6]  = (uint8_t)(o_vel >> 8);    // 回零速度(RPM)高8位字节
    cmd[7]  = (uint8_t)(o_vel >> 0);    // 回零速度(RPM)低8位字节
    cmd[8]  = (uint8_t)(o_tm >> 24);    // 回零超时时间(bit24-bit31)
    cmd[9]  = (uint8_t)(o_tm >> 16);    // 回零超时时间(bit16-bit23)
    cmd[10] = (uint8_t)(o_tm >> 8);     // 回零超时时间(bit8-bit15)
    cmd[11] = (uint8_t)(o_tm >> 0);     // 回零超时时间(bit0-bit7)
    cmd[12] = (uint8_t)(sl_vel >> 8);   // 碰撞限位检测转速(RPM)高8位字节
    cmd[13] = (uint8_t)(sl_vel >> 0);   // 碰撞限位检测转速(RPM)低8位字节
    cmd[14] = (uint8_t)(sl_ma >> 8);    // 碰撞限位检测电流(Ma)高8位字节
    cmd[15] = (uint8_t)(sl_ma >> 0);    // 碰撞限位检测电流(Ma)低8位字节
    cmd[16] = (uint8_t)(sl_ms >> 8);    // 碰撞限位检测时间(Ms)高8位字节
    cmd[17] = (uint8_t)(sl_ms >> 0);    // 碰撞限位检测时间(Ms)低8位字节
    cmd[18] = potF;                     // 上电自动触发回零，false为不使能，true为使能
    cmd[19] = 0x6B;                     // 校验字节

    // 发送命令
    EMM_V5_SendCmd(hemm, cmd, 20);
}
