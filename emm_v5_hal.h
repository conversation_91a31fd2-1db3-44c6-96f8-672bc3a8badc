#ifndef __EMM_V5_HAL_H
#define __EMM_V5_HAL_H

#include "stm32f1xx_hal.h"
#include <stdbool.h>
#include <stdint.h>

/**********************************************************
*** Emm_V5.0闭环步进电机驱动库 - HAL库移植版本
*** 原作者：ZHANGDATOU
*** HAL移植：Assistant
*** 支持平台：STM32F1xx HAL库
**********************************************************/

// 缓冲区大小定义
#define EMM_V5_RX_BUFFER_SIZE   128
#define EMM_V5_TX_BUFFER_SIZE   32
#define EMM_V5_CMD_TIMEOUT      1000    // 命令超时时间(ms)

// 系统参数枚举
typedef enum {
    S_VER   = 0,    /* 读取软件版本号和对应的硬件版本 */
    S_RL    = 1,    /* 读取读取或设置电机转向 */
    S_PID   = 2,    /* 读取PID参数 */
    S_VBUS  = 3,    /* 读取总线电压 */
    S_CPHA  = 5,    /* 读取相电流 */
    S_ENCL  = 7,    /* 读取经过多圈修正后的编码器值 */
    S_TPOS  = 8,    /* 读取电机目标位置角度 */
    S_VEL   = 9,    /* 读取电机实时转速 */
    S_CPOS  = 10,   /* 读取电机实时位置角度 */
    S_PERR  = 11,   /* 读取电机位置误差角度 */
    S_FLAG  = 13,   /* 读取使能/到位/堵转状态标志位 */
    S_Conf  = 14,   /* 读取系统配置 */
    S_State = 15,   /* 读取系统状态参数 */
    S_ORG   = 16,   /* 读取正在回零/回零失败状态标志位 */
} SysParams_t;

// 电机控制结构体
typedef struct {
    UART_HandleTypeDef *huart;          // UART句柄
    uint8_t rx_buffer[EMM_V5_RX_BUFFER_SIZE];  // 接收缓冲区
    uint8_t tx_buffer[EMM_V5_TX_BUFFER_SIZE];  // 发送缓冲区
    volatile bool rx_frame_flag;        // 接收帧标志
    volatile uint16_t rx_count;         // 接收数据计数
    volatile uint8_t rx_cmd[EMM_V5_RX_BUFFER_SIZE]; // 接收命令缓冲区
    uint32_t last_tx_time;              // 上次发送时间
    uint16_t rx_index;                  // 接收索引
} EMM_V5_HandleTypeDef;

// 函数声明
void EMM_V5_Init(EMM_V5_HandleTypeDef *hemm, UART_HandleTypeDef *huart);
void EMM_V5_UART_RxCpltCallback(EMM_V5_HandleTypeDef *hemm);
void EMM_V5_UART_IdleCallback(EMM_V5_HandleTypeDef *hemm);

// 基础控制函数
void EMM_V5_Reset_CurPos_To_Zero(EMM_V5_HandleTypeDef *hemm, uint8_t addr);
void EMM_V5_Reset_Clog_Pro(EMM_V5_HandleTypeDef *hemm, uint8_t addr);
void EMM_V5_Read_Sys_Params(EMM_V5_HandleTypeDef *hemm, uint8_t addr, SysParams_t s);
void EMM_V5_Modify_Ctrl_Mode(EMM_V5_HandleTypeDef *hemm, uint8_t addr, bool svF, uint8_t ctrl_mode);
void EMM_V5_En_Control(EMM_V5_HandleTypeDef *hemm, uint8_t addr, bool state, bool snF);

// 运动控制函数
void EMM_V5_Vel_Control(EMM_V5_HandleTypeDef *hemm, uint8_t addr, uint8_t dir, uint16_t vel, uint8_t acc, bool snF);
void EMM_V5_Pos_Control(EMM_V5_HandleTypeDef *hemm, uint8_t addr, uint8_t dir, uint16_t vel, uint8_t acc, uint32_t clk, bool raF, bool snF);
void EMM_V5_Stop_Now(EMM_V5_HandleTypeDef *hemm, uint8_t addr, bool snF);
void EMM_V5_Synchronous_motion(EMM_V5_HandleTypeDef *hemm, uint8_t addr);

// 回零控制函数
void EMM_V5_Origin_Set_O(EMM_V5_HandleTypeDef *hemm, uint8_t addr, bool svF);
void EMM_V5_Origin_Modify_Params(EMM_V5_HandleTypeDef *hemm, uint8_t addr, bool svF, uint8_t o_mode, uint8_t o_dir, 
                                uint16_t o_vel, uint32_t o_tm, uint16_t sl_vel, uint16_t sl_ma, uint16_t sl_ms, bool potF);
void EMM_V5_Origin_Trigger_Return(EMM_V5_HandleTypeDef *hemm, uint8_t addr, uint8_t o_mode, bool snF);
void EMM_V5_Origin_Interrupt(EMM_V5_HandleTypeDef *hemm, uint8_t addr);

// 工具函数
bool EMM_V5_Wait_Response(EMM_V5_HandleTypeDef *hemm, uint32_t timeout);
float EMM_V5_Parse_Position(EMM_V5_HandleTypeDef *hemm, uint8_t addr);
bool EMM_V5_Check_Response(EMM_V5_HandleTypeDef *hemm, uint8_t addr, uint8_t cmd);

// 宏定义
#define ABS(x)  ((x) > 0 ? (x) : -(x))

#endif /* __EMM_V5_HAL_H */
