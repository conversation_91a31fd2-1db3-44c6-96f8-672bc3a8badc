#include "board.h"
#include "delay.h"
#include "usart.h"
#include "Emm_V5.h"

/**********************************************************
***	Emm_V5.0�����ջ���������
***	��д���ߣ�ZHANGDATOU
***	����֧�֣��Ŵ�ͷ�ջ��ŷ�
***	�Ա����̣�https://zhangdatou.taobao.com
***	CSDN���ͣ�http s://blog.csdn.net/zhangdatou666
***	qq����Ⱥ��262438510
**********************************************************/

// ����ʵʱλ��ȫ�ֱ���
float pos = 0.0f, Motor_Cur_Pos = 0.0f;

/**
	*	@brief		MAIN����
	*	@param		��
	*	@retval		��
	*/
int main(void)
{
/**********************************************************
***	��ʼ����������
**********************************************************/
	board_init();

/**********************************************************
***	�ϵ���ʱ2��ȴ�Emm_V5.0�ջ���ʼ�����
**********************************************************/	
	delay_ms(2000);

/**********************************************************
***	λ��ģʽ������CW���ٶ�1000RPM�����ٶ�0����ʹ�üӼ���ֱ����������������3200��16ϸ���·���3200��������תһȦ��������˶�
**********************************************************/	
  Emm_V5_Pos_Control(1, 0, 1000, 0, 3200, 0, 0);
	
/**********************************************************
***	�ȴ���������������ݻ���������rxCmd�ϣ�����ΪrxCount
**********************************************************/	
	while(rxFrameFlag == false); rxFrameFlag = false;

/**********************************************************
***	��ʱ2�룬�ȴ��˶����
**********************************************************/	
  delay_ms(2000);

/**********************************************************
***	��ȡ���ʵʱλ��
**********************************************************/	
  Emm_V5_Read_Sys_Params(1, S_CPOS);

/**********************************************************
***	�ȴ���������������ݻ���������rxCmd�ϣ�����ΪrxCount
**********************************************************/	
	while(rxFrameFlag == false); rxFrameFlag = false;

/**********************************************************
***	У���ַ�������롢�������ݳ��ȣ�У��ɹ�����㵱ǰλ�ýǶ�
**********************************************************/	
  if(rxCmd[0] == 1 && rxCmd[1] == 0x36 && rxCount == 8)
  {
    // ƴ�ӳ�uint32_t����
    pos = (uint32_t)(
                      ((uint32_t)rxCmd[3] << 24)    |
                      ((uint32_t)rxCmd[4] << 16)    |
                      ((uint32_t)rxCmd[5] << 8)     |
                      ((uint32_t)rxCmd[6] << 0)
                    );

    // ת���ɽǶ�
    Motor_Cur_Pos = (float)pos * 360.0f / 65536.0f;

    // ����
    if(rxCmd[2]) { Motor_Cur_Pos = -Motor_Cur_Pos; }
  }

/**********************************************************
***	WHILEѭ��
**********************************************************/	
	while(1)
	{
	}
}
